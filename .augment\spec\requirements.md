# DeepSeek加密货币永续合约全自动量化系统 需求文档

## 功能概述
基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，通过技术分析指标和独立的AI开仓引擎、AI持仓引擎实现智能化交易。系统采用Python后端（FastAPI）+ Jinja2模板前端，集成欧易交易所API，支持模拟盘和实盘切换，实现全自动的开仓、持仓管理和风险控制。

## 需求列表

### 1. 交易所集成与数据获取
**用户故事：** 作为量化交易者，我希望系统能够连接欧易交易所并获取实时市场数据，以便进行技术分析和交易决策。

**验收标准：**
1. 当系统启动时，应该能够通过CCXT库成功连接到欧易交易所（OKX）
2. 当需要市场数据时，系统应该能够获取1分钟、5分钟、15分钟、1小时的K线数据
3. 当获取数据时，系统应该包含开高低收价格和成交量信息
4. 当用户切换模拟盘/实盘时，系统应该能够无缝切换API端点
5. 当网络异常时，系统应该有重试机制和错误处理

### 2. 技术分析计算引擎
**用户故事：** 作为量化交易者，我希望系统能够基于多时间周期的市场数据计算技术指标，以便为AI决策提供数据支持。

**验收标准：**
1. 当获取到市场数据时，系统应该使用TA-Lib计算常用技术指标（MA、RSI、MACD、布林带等）
2. 当计算指标时，系统应该结合1分、5分、15分、1小时四个时间周期的数据
3. 当指标计算完成时，系统应该将结果格式化为AI模型可理解的数据结构
4. 当数据不足时，系统应该等待足够的历史数据再进行计算
5. 当计算出错时，系统应该记录错误并跳过该次计算

### 3. AI开仓引擎
**用户故事：** 作为量化交易者，我希望系统能够将技术分析结果发送给DeepSeek AI模型的开仓引擎并获得开仓建议，然后直接调用交易所客户端执行开仓操作，以便识别并抓住新的交易机会。

**验收标准：**
1. 当技术指标计算完成且无持仓时，系统应该将数据发送给DeepSeek AI开仓引擎
2. 当AI开仓引擎分析时，系统应该使用专门的开仓提示词模板
3. 当AI返回开仓建议时，系统应该获得开仓方向（做多/做空）、置信度（0-100）和决策理由
4. 当开仓置信度超过设定阈值时，系统应该直接调用交易所客户端的开仓方法
5. 当AI开仓服务不可用时，系统应该停止开仓并记录错误

### 4. AI持仓引擎
**用户故事：** 作为量化交易者，我希望系统能够将当前持仓和市场数据发送给DeepSeek AI模型的持仓引擎并获得持仓管理建议，然后直接调用交易所客户端执行持仓操作，以便优化现有持仓。

**验收标准：**
1. 当存在持仓且技术指标更新时，系统应该将持仓信息和市场数据发送给DeepSeek AI持仓引擎
2. 当AI持仓引擎分析时，系统应该使用专门的持仓管理提示词模板
3. 当AI返回持仓建议时，系统应该获得操作建议（持有/平仓/调整止损）、置信度（0-100）和决策理由
4. 当持仓置信度超过设定阈值时，系统应该直接调用交易所客户端的相应方法
5. 当AI持仓服务不可用时，系统应该保持当前持仓状态并记录错误

### 5. 交易所客户端模块
**用户故事：** 作为量化交易者，我希望系统能够提供一个统一的交易所客户端接口，封装所有CCXT功能，以便AI引擎能够直接调用执行各种交易操作。

**验收标准：**
1. 当系统初始化时，交易所客户端应该基于CCXT库连接到欧易交易所
2. 当需要开仓时，客户端应该提供开仓方法，包含仓位计算、杠杆设置、风险控制
3. 当需要平仓时，客户端应该提供平仓方法，支持部分平仓和全部平仓
4. 当需要调整止损时，客户端应该提供止损调整方法
5. 当需要查询信息时，客户端应该提供账户余额、持仓信息、订单状态等查询方法
6. 当执行交易时，客户端应该支持做多和做空两个方向（单向持仓）
7. 当交易完成时，客户端应该返回详细的执行结果和错误信息
8. 当网络异常时，客户端应该有重试机制和异常处理

### 6. 风险管理系统
**用户故事：** 作为量化交易者，我希望系统能够严格控制交易风险，以便保护我的投资资金。

**验收标准：**
1. 当计算仓位时，系统应该基于可用资金而非总资产
2. 当设置止损时，系统应该考虑杠杆放大效应
3. 当达到最大仓位限制时，系统应该拒绝新的开仓请求
4. 当账户余额不足时，系统应该停止交易并发出警告
5. 当风险指标异常时，系统应该自动降低仓位或停止交易

### 7. 数据存储管理
**用户故事：** 作为量化交易者，我希望系统能够安全存储配置信息，以便系统正常运行和参数管理。

**验收标准：**
1. 当系统初始化时，应该创建SQLite3数据库存储配置信息
2. 当存储敏感信息时，系统应该对API密钥进行加密存储
3. 当存储设置时，系统应该包含交易参数、风险控制参数等
4. 当查询配置时，系统应该能够快速检索和更新参数
5. 当数据库操作失败时，系统应该有备份和恢复机制

### 8. Web前端界面
**用户故事：** 作为量化交易者，我希望有一个直观易用的Web界面来监控和控制交易系统，以便实时了解系统状态。

**验收标准：**
1. 当访问系统时，应该提供基于Jinja2模板和Ant Design风格的界面
2. 当查看仪表板时，应该显示账户余额、持仓信息、交易历史等
3. 当设置参数时，应该提供表单来配置最大杠杆、最大仓位、止盈止损等
4. 当选择交易对时，应该提供多选界面来选择要交易的币种
5. 当系统运行时，应该实时更新交易状态和AI决策信息

### 10. 系统配置管理
**用户故事：** 作为量化交易者，我希望能够灵活配置系统参数，以便根据市场情况调整交易策略。

**验收标准：**
1. 当配置API时，系统应该支持输入欧易交易所的API密钥和密码
2. 当设置交易参数时，应该包含最大杠杆倍数、最大仓位比例等
3. 当配置风控时，应该设置止盈止损百分比、最大回撤等参数
4. 当选择模式时，应该支持模拟盘和实盘的切换
5. 当保存配置时，系统应该验证参数的有效性和合理性

### 11. 系统监控与日志
**用户故事：** 作为量化交易者，我希望系统能够提供详细的运行日志和监控信息，以便了解系统运行状态和排查问题。

**验收标准：**
1. 当系统运行时，应该记录所有交易操作和AI决策过程
2. 当发生错误时，应该详细记录错误信息和堆栈跟踪
3. 当查看日志时，应该提供不同级别的日志过滤和搜索功能
4. 当系统异常时，应该及时发出警告并记录异常状态
5. 当需要调试时，应该提供详细的系统运行状态信息

### 12. 交易执行引擎
**用户故事：** 作为量化交易者，我希望系统能够可靠地执行交易指令，以便确保交易策略的有效实施。

**验收标准：**
1. 当执行交易时，系统应该使用保证金交易模式和全仓模式
2. 当下单时，系统应该验证订单参数的正确性
3. 当订单执行时，系统应该监控订单状态直到完成
4. 当订单失败时，系统应该记录失败原因并进行重试或跳过
5. 当网络延迟时，系统应该有超时处理和重连机制
