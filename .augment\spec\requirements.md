# DeepSeek加密货币永续合约全自动量化系统 需求文档

## 功能概述
基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统。系统通过技术分析指标结合独立的AI开仓引擎和AI持仓引擎实现智能化交易决策，采用统一的交易所客户端封装CCXT功能，支持多个主流交易所的模拟盘和实盘切换，实现全自动的风险控制交易。

## 需求列表

### 1. 交易所客户端模块
**用户故事：** 作为量化交易者，我希望系统提供一个统一的交易所客户端，封装CCXT支持的多个交易所功能，以便AI引擎能够直接调用执行各种交易操作。

**验收标准：**
1. 当系统启动时，客户端应该基于CCXT库成功连接到用户选择的交易所（支持欧易OKX、币安Binance、火币Huobi等CCXT支持的主流交易所）
2. 当需要市场数据时，客户端应该通过CCXT统一接口获取K线数据
3. 当获取交易对信息时，客户端应该通过CCXT统一接口获取所有可用永续合约交易对列表
4. 当查询合约规格时，客户端应该通过CCXT统一接口获取合约张数精度、最小下单量、价格精度等信息
5. 当执行开仓时，客户端应该通过CCXT统一接口执行开仓操作，支持做多做空、杠杆设置、仓位计算
6. 当执行平仓时，客户端应该通过CCXT统一接口执行平仓操作，支持部分平仓和全部平仓
7. 当调整订单时，客户端应该通过CCXT统一接口修改止盈止损订单
8. 当查询信息时，客户端应该通过CCXT统一接口查询账户余额、持仓信息、订单状态
9. 当切换环境时，客户端应该支持模拟盘和实盘的无缝切换（如果交易所支持）
10. 当切换交易所时，客户端应该能够动态切换CCXT交易所实例
11. 当网络异常时，客户端应该有重试机制和完善的错误处理

### 2. 市场数据获取引擎
**用户故事：** 作为量化交易者，我希望系统能够持续获取多时间周期的市场数据，以便为技术分析和AI决策提供数据基础。

**验收标准：**
1. 当系统运行时，应该通过调用交易所客户端的K线数据获取功能来获取实时市场数据
2. 当获取数据时，应该调用客户端同时获取1分钟、5分钟、15分钟、1小时四个时间周期的K线数据
3. 当处理K线数据时，应该确保包含开高低收价格和成交量信息
4. 当数据更新时，应该按照设定的轮询间隔定期调用客户端刷新数据
5. 当数据获取失败时，应该记录错误并继续尝试调用客户端获取

### 3. 技术分析计算引擎
**用户故事：** 作为量化交易者，我希望系统能够基于多时间周期的市场数据计算多种技术指标，以便为AI决策提供全面的技术分析支持。

**验收标准：**
1. 当获取到市场数据时，系统应该使用TA-Lib计算以下技术指标：
   - 趋势指标：SMA（简单移动平均）、EMA（指数移动平均）、MACD（异同移动平均）、ADX（平均趋向指数）
   - 震荡指标：RSI（相对强弱指数）、Stochastic（随机指标）、Williams %R、CCI（商品通道指数）
   - 波动率指标：Bollinger Bands（布林带）、ATR（真实波动幅度）、Keltner Channel（肯特纳通道）
   - 成交量指标：OBV（能量潮）、Volume SMA、VWAP（成交量加权平均价）
   - 支撑阻力：Pivot Points（枢轴点）、Fibonacci回调位
2. 当计算指标时，系统应该对1分钟、5分钟、15分钟、1小时四个时间周期分别计算所有指标
3. 当指标计算完成时，系统应该将多时间周期的指标结果格式化为AI模型可理解的结构化数据
4. 当历史数据不足时，系统应该等待足够的数据点（至少200个K线）再进行计算
5. 当计算异常时，系统应该跳过该次计算并记录错误信息，继续处理其他指标

### 4. AI开仓引擎
**用户故事：** 作为量化交易者，我希望AI开仓引擎能够分析市场数据并给出开仓建议，然后直接调用交易所客户端执行开仓，以便自动抓住交易机会。

**验收标准：**
1. 当技术指标计算完成且当前无持仓时，系统应该将数据发送给DeepSeek AI开仓引擎
2. 当AI分析时，系统应该使用专门设计的开仓提示词模板
3. 当AI返回结果时，系统应该获得开仓方向（做多/做空）、置信度（0-100）和决策理由
4. 当置信度超过设定阈值时，系统应该调用交易所客户端的开仓方法
5. 当AI服务异常时，系统应该停止开仓决策并记录错误

### 5. AI持仓引擎
**用户故事：** 作为量化交易者，我希望AI持仓引擎能够管理现有持仓并给出持仓建议，然后直接调用交易所客户端执行操作，以便优化持仓收益。

**验收标准：**
1. 当存在持仓且市场数据更新时，系统应该将持仓和市场信息发送给DeepSeek AI持仓引擎
2. 当AI分析时，系统应该使用专门设计的持仓管理提示词模板
3. 当AI返回结果时，系统应该获得操作建议（持有/平仓/调整止损）、置信度和决策理由
4. 当置信度超过设定阈值时，系统应该调用交易所客户端的相应方法
5. 当AI服务异常时，系统应该保持当前持仓状态并记录错误

### 6. 风险管理系统
**用户故事：** 作为量化交易者，我希望系统能够严格控制交易风险，以便保护投资资金安全。

**验收标准：**
1. 当计算仓位时，系统应该基于可用资金而非总资产进行计算
2. 当设置杠杆时，系统应该不超过用户设定的最大杠杆倍数
3. 当计算止损时，系统应该考虑杠杆放大效应和持仓方向
4. 当达到最大仓位时，系统应该拒绝新的开仓请求
5. 当账户余额不足时，系统应该停止交易并发出警告

### 7. 数据存储管理
**用户故事：** 作为量化交易者，我希望系统能够安全存储配置信息，以便系统正常运行。

**验收标准：**
1. 当系统初始化时，应该创建SQLite3数据库存储系统配置
2. 当存储API密钥时，应该进行加密存储保证安全性
3. 当存储交易参数时，应该包含杠杆、仓位、止盈止损等设置
4. 当查询配置时，应该能够快速检索和更新参数
5. 当数据库异常时，应该有备份和恢复机制

### 8. Web前端界面
**用户故事：** 作为量化交易者，我希望有一个控制台风格的Web界面来监控和配置交易系统，以便专业化地管理交易操作。

**验收标准：**
1. 当访问系统时，应该提供基于Jinja2模板和Ant Design的深色控制台风格界面
2. 当进入主页时，应该显示控制台仪表板布局，包含实时滚动的系统状态、账户总资产、可用余额、当日盈亏
3. 当查看持仓信息时，应该在控制台表格中显示当前持仓，包含交易对、方向、数量、开仓价格、当前价格、浮动盈亏、止盈止损价格
4. 当查看AI决策时，应该在控制台日志区域实时显示AI开仓和持仓引擎决策，包含时间戳、交易对、决策类型、置信度、理由、执行状态
5. 当进入设置页面时，应该提供交易所选择下拉菜单，支持选择CCXT支持的多个交易所（欧易OKX、币安Binance、火币Huobi等主流交易所）
6. 当配置交易参数时，应该提供控制台风格的配置面板，设置最大杠杆倍数（1-100倍）、最大仓位比例（1-100%）、开仓置信度阈值（0-100）、持仓置信度阈值（0-100）
7. 当配置风控参数时，应该提供风险控制面板，设置默认止盈百分比、默认止损百分比、最大回撤限制
8. 当选择交易对时，应该提供控制台风格的多选列表，显示所有可用的永续合约交易对，支持快速搜索和批量选择
9. 当配置API时，应该提供安全的API配置面板，输入选定交易所的API Key、Secret Key、Passphrase等，并支持模拟盘/实盘环境切换
10. 当查看系统日志时，应该提供控制台终端风格的日志显示区域，支持实时滚动、按级别筛选、关键词搜索
11. 当系统运行时，应该通过WebSocket或定时刷新实时更新控制台数据，包括账户余额、持仓状态、AI决策结果
12. 当操作系统时，应该提供控制台风格的操作按钮（启动/停止/重启），并在状态栏显示系统运行状态（运行中/已停止/异常/连接中）
13. 当发生错误时，应该在控制台通知区域显示错误信息，使用不同颜色标识错误级别（警告/错误/致命）
14. 当切换功能模块时，应该提供侧边栏导航菜单，包含仪表板、持仓管理、AI决策、交易设置、系统日志等模块

### 9. 系统配置管理
**用户故事：** 作为量化交易者，我希望能够灵活配置系统参数，以便适应不同的交易策略。

**验收标准：**
1. 当配置API时，应该支持输入不同交易所的API密钥和相关认证信息
2. 当设置交易参数时，应该包含最大杠杆、最大仓位比例、置信度阈值
3. 当配置风控时，应该设置止盈止损百分比、最大回撤限制
4. 当选择模式时，应该支持模拟盘和实盘的切换
5. 当保存配置时，应该验证参数的有效性和合理性

### 10. 系统监控与日志
**用户故事：** 作为量化交易者，我希望系统提供详细的监控和日志功能，以便了解系统运行状态。

**验收标准：**
1. 当系统运行时，应该记录所有AI决策过程和交易操作
2. 当发生异常时，应该详细记录错误信息和处理结果
3. 当查看日志时，应该提供分级过滤和关键词搜索功能
4. 当系统状态异常时，应该及时发出警告通知
5. 当需要调试时，应该提供详细的系统运行状态信息