# DeepSeek加密货币永续合约全自动量化系统 设计文档

## 概述
本系统是基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统。系统采用模块化架构，通过统一的交易所客户端封装CCXT功能，支持多个主流交易所，结合独立的AI开仓引擎和AI持仓引擎实现智能化交易决策。

### 核心设计原则
- **单一职责**：每个模块职责明确，功能独立
- **统一接口**：通过CCXT提供统一的交易所访问接口
- **AI驱动**：基于DeepSeek AI模型的智能决策
- **风险优先**：严格的风险控制和参数验证
- **可扩展性**：支持多交易所，易于扩展新功能

## 项目结构

```
Superbot/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖包列表
├── .env.example                       # 环境变量示例文件
├── .gitignore                         # Git忽略文件配置
├── main.py                           # 应用程序入口点
├── config/                           # 配置文件目录
│   ├── __init__.py
│   ├── settings.py                   # 应用配置设置
│   ├── logging_config.py             # 日志配置
│   └── indicators_config.py          # 技术指标配置
├── src/                              # 源代码目录
│   ├── __init__.py
│   ├── core/                         # 核心交易引擎
│   │   ├── __init__.py
│   │   ├── exchange_client.py        # 交易所客户端
│   │   ├── market_data_engine.py     # 市场数据获取引擎
│   │   ├── technical_analysis.py     # 技术分析计算引擎
│   │   ├── ai_opening_engine.py      # AI开仓引擎
│   │   ├── ai_position_engine.py     # AI持仓引擎
│   │   ├── risk_manager.py           # 风险管理系统
│   │   └── trading_coordinator.py    # 交易协调器
│   ├── data/                         # 数据管理
│   │   ├── __init__.py
│   │   ├── database_manager.py       # 数据库管理
│   │   ├── models.py                 # 数据模型定义
│   │   └── encryption.py             # 数据加密工具
│   ├── ai/                           # AI相关模块
│   │   ├── __init__.py
│   │   ├── deepseek_client.py        # DeepSeek API客户端
│   │   ├── prompt_templates.py       # AI提示词模板
│   │   └── response_parser.py        # AI响应解析器
│   ├── web/                          # Web前端和API
│   │   ├── __init__.py
│   │   ├── app.py                    # FastAPI应用主文件
│   │   ├── routes/                   # API路由
│   │   │   ├── __init__.py
│   │   │   ├── dashboard.py          # 仪表板API
│   │   │   ├── trading.py            # 交易相关API
│   │   │   ├── config.py             # 配置管理API
│   │   │   └── system.py             # 系统控制API
│   │   ├── templates/                # Jinja2模板
│   │   │   ├── base.html             # 基础模板
│   │   │   ├── dashboard.html        # 仪表板页面
│   │   │   ├── positions.html        # 持仓管理页面
│   │   │   ├── ai_decisions.html     # AI决策页面
│   │   │   ├── settings.html         # 系统设置页面
│   │   │   └── logs.html             # 日志查看页面
│   │   └── static/                   # 静态资源
│   │       ├── css/                  # 样式文件
│   │       │   ├── console.css       # 控制台风格样式
│   │       │   └── antd.css          # Ant Design样式
│   │       ├── js/                   # JavaScript文件
│   │       │   ├── dashboard.js      # 仪表板脚本
│   │       │   ├── trading.js        # 交易相关脚本
│   │       │   └── utils.js          # 工具函数
│   │       └── images/               # 图片资源
│   ├── utils/                        # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py                 # 日志工具
│   │   ├── validators.py             # 数据验证工具
│   │   ├── formatters.py             # 数据格式化工具
│   │   └── exceptions.py             # 自定义异常类
│   └── services/                     # 服务层
│       ├── __init__.py
│       ├── config_service.py         # 配置管理服务
│       ├── monitoring_service.py     # 系统监控服务
│       └── notification_service.py   # 通知服务
├── tests/                            # 测试目录
│   ├── __init__.py
│   ├── conftest.py                   # pytest配置
│   ├── unit/                         # 单元测试
│   │   ├── __init__.py
│   │   ├── test_exchange_client.py
│   │   ├── test_market_data_engine.py
│   │   ├── test_technical_analysis.py
│   │   ├── test_ai_engines.py
│   │   ├── test_risk_manager.py
│   │   └── test_database_manager.py
│   ├── integration/                  # 集成测试
│   │   ├── __init__.py
│   │   ├── test_trading_flow.py
│   │   ├── test_ai_integration.py
│   │   └── test_web_api.py
│   └── fixtures/                     # 测试数据
│       ├── market_data.json
│       ├── ai_responses.json
│       └── config_samples.json
├── docs/                             # 文档目录
│   ├── api_documentation.md          # API文档
│   ├── user_guide.md                 # 用户指南
│   ├── deployment_guide.md           # 部署指南
│   └── troubleshooting.md            # 故障排除
├── scripts/                          # 脚本目录
│   ├── setup_database.py             # 数据库初始化脚本
│   ├── backup_config.py              # 配置备份脚本
│   └── health_check.py               # 健康检查脚本
├── logs/                             # 日志目录
│   ├── trading.log                   # 交易日志
│   ├── ai_decisions.log              # AI决策日志
│   ├── system.log                    # 系统日志
│   └── error.log                     # 错误日志
└── data/                             # 数据目录
    ├── trading_system.db             # SQLite数据库文件
    ├── backups/                      # 备份目录
    └── temp/                         # 临时文件目录
```

### 核心文件说明

**主要入口文件**：
- `main.py` - 应用程序启动入口，初始化所有组件
- `src/web/app.py` - FastAPI Web应用主文件

**核心交易模块**：
- `src/core/exchange_client.py` - 封装CCXT的交易所客户端
- `src/core/market_data_engine.py` - 市场数据获取和管理
- `src/core/technical_analysis.py` - 技术指标计算引擎
- `src/core/ai_opening_engine.py` - AI开仓决策引擎
- `src/core/ai_position_engine.py` - AI持仓管理引擎
- `src/core/trading_coordinator.py` - 交易流程协调器

**AI相关模块**：
- `src/ai/deepseek_client.py` - DeepSeek API客户端封装
- `src/ai/prompt_templates.py` - 开仓和持仓的提示词模板
- `src/ai/response_parser.py` - AI响应解析和验证

**数据管理模块**：
- `src/data/database_manager.py` - SQLite数据库操作
- `src/data/models.py` - 数据模型和结构定义
- `src/data/encryption.py` - API密钥加密解密

**Web界面模块**：
- `src/web/routes/` - FastAPI路由定义
- `src/web/templates/` - Jinja2模板文件
- `src/web/static/` - 前端静态资源

**配置和工具**：
- `config/settings.py` - 应用配置管理
- `src/utils/` - 通用工具函数
- `src/services/` - 业务服务层

## 架构设计

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Web前端界面 (控制台风格)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  仪表板     │ │  持仓管理   │ │  AI决策     │ │  系统设置   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/AJAX
┌─────────────────────────────────────────────────────────────┐
│                      FastAPI 后端服务                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              系统配置管理                                │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              风险管理系统                                │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              系统监控与日志                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      核心交易引擎                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 交易所客户端 │ │市场数据引擎 │ │技术分析引擎 │ │数据存储管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐                               │
│  │ AI开仓引擎  │ │ AI持仓引擎  │                               │
│  └─────────────┘ └─────────────┘                               │
└─────────────────────────────────────────────────────────────┘
                              │ CCXT
┌─────────────────────────────────────────────────────────────┐
│                      交易所接口层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   欧易OKX   │ │  币安Binance │ │  火币Huobi  │ │    其他     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 数据流设计
```
市场数据流：
交易所API → 交易所客户端 → 市场数据获取引擎 → 技术分析计算引擎

AI决策流：
技术分析结果 → AI开仓引擎/AI持仓引擎 → DeepSeek API → 交易决策

交易执行流：
AI决策 → 风险管理验证 → 交易所客户端 → CCXT → 交易所API

配置管理流：
Web前端 → 系统配置管理 → SQLite3数据库 → 各模块配置更新
```

## 组件和接口

### 1. 交易所客户端模块
**职责**：封装CCXT功能，提供统一的交易所访问接口

**核心类设计**：
```python
class ExchangeClient:
    def __init__(self, exchange_name: str, api_config: dict, sandbox: bool = True)
    
    # 连接管理
    def connect(self) -> bool
    def disconnect(self) -> None
    def is_connected(self) -> bool
    
    # 市场数据
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 200) -> List[dict]
    def fetch_markets(self) -> List[dict]
    def fetch_market_info(self, symbol: str) -> dict
    
    # 账户信息
    def fetch_balance(self) -> dict
    def fetch_positions(self) -> List[dict]
    def fetch_orders(self, symbol: str = None) -> List[dict]
    
    # 交易操作
    def create_market_order(self, symbol: str, side: str, amount: float, leverage: int = 1) -> dict
    def create_limit_order(self, symbol: str, side: str, amount: float, price: float) -> dict
    def cancel_order(self, order_id: str, symbol: str) -> dict
    def close_position(self, symbol: str, side: str, amount: float = None) -> dict
    
    # 单向持仓约束
    def ensure_single_direction(self, symbol: str, new_side: str) -> bool
```

**接口规范**：
- 所有方法返回标准化的数据格式
- 统一的错误处理和重试机制
- 支持多交易所的动态切换

### 2. 市场数据获取引擎
**职责**：定期获取多时间周期的市场数据

**核心类设计**：
```python
class MarketDataEngine:
    def __init__(self, exchange_client: ExchangeClient, symbols: List[str])
    
    # 数据获取
    def fetch_multi_timeframe_data(self, symbol: str) -> dict
    def start_data_polling(self, interval: int = 60) -> None
    def stop_data_polling(self) -> None
    
    # 数据管理
    def get_latest_data(self, symbol: str, timeframe: str) -> List[dict]
    def is_data_sufficient(self, symbol: str, timeframe: str, min_periods: int = 200) -> bool
```

**数据格式**：
```python
{
    "symbol": "BTC/USDT:USDT",
    "timeframes": {
        "1m": [{"timestamp": 1234567890, "open": 50000, "high": 50100, "low": 49900, "close": 50050, "volume": 1000}],
        "5m": [...],
        "15m": [...],
        "1h": [...]
    },
    "last_update": 1234567890
}
```

### 3. 技术分析计算引擎
**职责**：计算多种技术指标，为AI决策提供数据支持

**核心类设计**：
```python
class TechnicalAnalysisEngine:
    def __init__(self)
    
    # 指标计算
    def calculate_trend_indicators(self, ohlcv_data: List[dict]) -> dict
    def calculate_oscillator_indicators(self, ohlcv_data: List[dict]) -> dict
    def calculate_volatility_indicators(self, ohlcv_data: List[dict]) -> dict
    def calculate_volume_indicators(self, ohlcv_data: List[dict]) -> dict
    def calculate_support_resistance(self, ohlcv_data: List[dict]) -> dict
    
    # 综合分析
    def analyze_multi_timeframe(self, market_data: dict) -> dict
    def format_for_ai(self, indicators: dict) -> str
```

**技术指标配置**：
```python
INDICATORS_CONFIG = {
    "trend": ["SMA_20", "SMA_50", "EMA_12", "EMA_26", "MACD", "ADX"],
    "oscillator": ["RSI_14", "STOCH_K", "STOCH_D", "WILLIAMS_R", "CCI"],
    "volatility": ["BB_UPPER", "BB_LOWER", "ATR", "KELTNER_UPPER", "KELTNER_LOWER"],
    "volume": ["OBV", "VOLUME_SMA", "VWAP"],
    "support_resistance": ["PIVOT_POINT", "FIBONACCI_RETRACEMENT"]
}
```

### 4. AI开仓引擎
**职责**：在无持仓时分析市场数据，给出开仓建议

**核心类设计**：
```python
class AIOpeningEngine:
    def __init__(self, deepseek_api_key: str)

    # AI分析
    def analyze_market_for_opening(self, technical_data: dict, symbol: str) -> dict
    def should_open_position(self, confidence: float, threshold: float) -> bool

    # 提示词管理
    def build_opening_prompt(self, technical_data: dict, symbol: str) -> str
    def parse_ai_response(self, response: str) -> dict
```

**AI响应格式**：
```python
{
    "action": "open_long" | "open_short" | "no_action",
    "confidence": 85,  # 0-100
    "reasoning": "基于技术指标分析，RSI显示超卖，MACD金叉，建议开多头仓位",
    "suggested_leverage": 10,
    "risk_level": "medium"
}
```

### 5. AI持仓引擎
**职责**：在有持仓时分析持仓状态，给出持仓管理建议

**核心类设计**：
```python
class AIPositionEngine:
    def __init__(self, deepseek_api_key: str)

    # AI分析
    def analyze_position_management(self, position_data: dict, technical_data: dict, profit_drawdown: dict) -> dict
    def should_manage_position(self, confidence: float, threshold: float) -> bool

    # 利润回撤计算
    def calculate_profit_drawdown(self, position: dict, current_price: float) -> dict

    # 提示词管理
    def build_position_prompt(self, position_data: dict, technical_data: dict, profit_drawdown: dict) -> str
    def parse_ai_response(self, response: str) -> dict
```

**持仓数据格式**：
```python
{
    "symbol": "BTC/USDT:USDT",
    "side": "long" | "short",
    "amount": 0.1,
    "entry_price": 50000,
    "current_price": 52000,
    "leverage": 10,
    "unrealized_pnl": 200,
    "unrealized_pnl_percentage": 4.0,
    "profit_drawdown": {
        "max_profit_rate": 8.0,  # 历史最高收益率
        "current_profit_rate": 4.0,  # 当前收益率
        "drawdown_amount": 4.0,  # 回撤幅度
        "drawdown_percentage": 50.0  # 回撤比例
    }
}
```

### 6. 风险管理系统
**职责**：进行基础的风险控制，确保不超过用户设置的参数限制

**核心类设计**：
```python
class RiskManager:
    def __init__(self, config: dict)

    # 风险检查
    def validate_opening_order(self, order: dict, balance: dict) -> tuple[bool, str]
    def validate_leverage(self, leverage: int, max_leverage: int) -> bool
    def validate_position_size(self, amount: float, balance: float, max_position_ratio: float) -> bool
    def validate_available_balance(self, required_margin: float, available_balance: float) -> bool

    # 参数验证
    def validate_risk_parameters(self, params: dict) -> tuple[bool, List[str]]
```

**风险参数配置**：
```python
{
    "max_leverage": 20,
    "max_position_ratio": 0.8,  # 最大仓位比例 80%
    "min_balance_threshold": 100,  # 最小余额阈值
    "opening_confidence_threshold": 70,
    "position_confidence_threshold": 60
}
```

### 7. 数据存储管理
**职责**：管理SQLite3数据库，存储配置信息

**核心类设计**：
```python
class DatabaseManager:
    def __init__(self, db_path: str = "trading_system.db")

    # 数据库操作
    def init_database(self) -> None
    def encrypt_api_keys(self, api_data: dict) -> dict
    def decrypt_api_keys(self, encrypted_data: dict) -> dict

    # 配置管理
    def save_exchange_config(self, config: dict) -> bool
    def load_exchange_config(self) -> dict
    def save_trading_parameters(self, params: dict) -> bool
    def load_trading_parameters(self) -> dict
    def save_risk_parameters(self, params: dict) -> bool
    def load_risk_parameters(self) -> dict
    def save_selected_symbols(self, symbols: List[str]) -> bool
    def load_selected_symbols(self) -> List[str]
```

**数据库表结构**：
```sql
-- 交易所配置表
CREATE TABLE exchange_config (
    id INTEGER PRIMARY KEY,
    exchange_name TEXT NOT NULL,
    api_key TEXT NOT NULL,
    secret_key TEXT NOT NULL,
    passphrase TEXT,
    sandbox_mode BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 交易参数表
CREATE TABLE trading_parameters (
    id INTEGER PRIMARY KEY,
    max_leverage INTEGER DEFAULT 10,
    max_position_ratio REAL DEFAULT 0.5,
    opening_confidence_threshold INTEGER DEFAULT 70,
    position_confidence_threshold INTEGER DEFAULT 60,
    default_stop_loss_percentage REAL DEFAULT 0.05,
    default_take_profit_percentage REAL DEFAULT 0.1,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 选择的交易对表
CREATE TABLE selected_symbols (
    id INTEGER PRIMARY KEY,
    symbol TEXT NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY,
    level TEXT NOT NULL,
    module TEXT NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 数据模型

### 市场数据模型
```python
@dataclass
class OHLCVData:
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float

@dataclass
class TechnicalIndicators:
    symbol: str
    timeframe: str
    trend_indicators: dict
    oscillator_indicators: dict
    volatility_indicators: dict
    volume_indicators: dict
    support_resistance: dict
    calculated_at: int
```

### 交易数据模型
```python
@dataclass
class Position:
    symbol: str
    side: str  # 'long' or 'short'
    amount: float
    entry_price: float
    current_price: float
    leverage: int
    unrealized_pnl: float
    unrealized_pnl_percentage: float
    margin_used: float

@dataclass
class ProfitDrawdown:
    max_profit_rate: float
    current_profit_rate: float
    drawdown_amount: float
    drawdown_percentage: float

@dataclass
class AIDecision:
    action: str
    confidence: int
    reasoning: str
    timestamp: int
    symbol: str
    engine_type: str  # 'opening' or 'position'
```

### 配置数据模型
```python
@dataclass
class ExchangeConfig:
    exchange_name: str
    api_key: str
    secret_key: str
    passphrase: str = None
    sandbox_mode: bool = True

@dataclass
class TradingParameters:
    max_leverage: int = 10
    max_position_ratio: float = 0.5
    opening_confidence_threshold: int = 70
    position_confidence_threshold: int = 60
    default_stop_loss_percentage: float = 0.05
    default_take_profit_percentage: float = 0.1

@dataclass
class RiskParameters:
    max_leverage: int
    max_position_ratio: float
    min_balance_threshold: float
    opening_confidence_threshold: int
    position_confidence_threshold: int
```

## 错误处理

### 错误分类和处理策略
```python
class TradingSystemError(Exception):
    """交易系统基础异常"""
    pass

class ExchangeConnectionError(TradingSystemError):
    """交易所连接异常"""
    # 处理策略：重试连接，记录错误，暂停交易

class InsufficientBalanceError(TradingSystemError):
    """余额不足异常"""
    # 处理策略：拒绝交易，发出警告

class AIServiceError(TradingSystemError):
    """AI服务异常"""
    # 处理策略：保持当前状态，记录错误，等待恢复

class RiskViolationError(TradingSystemError):
    """风险控制违规异常"""
    # 处理策略：拒绝操作，记录原因

class DataInsufficientError(TradingSystemError):
    """数据不足异常"""
    # 处理策略：等待更多数据，跳过当前分析
```

### 错误恢复机制
- **网络异常**：指数退避重试，最大重试次数限制
- **API限制**：遵循交易所API限制，实现请求频率控制
- **数据异常**：数据验证，异常数据过滤
- **AI服务异常**：降级策略，保持当前状态

## 测试策略

### 单元测试
- 每个模块的核心功能测试
- 数据模型验证测试
- 错误处理测试

### 集成测试
- 模块间接口测试
- 数据流测试
- AI引擎集成测试

### 系统测试
- 完整交易流程测试
- 风险控制测试
- 性能压力测试

### 模拟交易测试
- 使用交易所沙盒环境
- 真实市场数据回测
- AI决策效果验证

**设计看起来如何？如果满意，我们可以进入实施计划。**
```
